{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,yBAAyB,EAAE,aAAa,OAAO,eAAe,aAAa;QACvF,OAAO;YACL,YAAY;QACd;;0BAIA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,iDAAiD,EAAE,aAAa,OAAO,YAAY,UAAU;;;;;;kCAC9G,8OAAC;wBAAI,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,YAAY,YAAY;;;;;;kCAC5H,8OAAC;wBAAI,WAAW,CAAC,mDAAmD,EAAE,aAAa,OAAO,aAAa,aAAa;;;;;;;;;;;;0BAItH,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAW,CAAC,kDAAkD,EAAE,aAAa,OAAO,qCAAqC,IAAI;sCAC9H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,8OAAC;4CACC,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;4CACtE,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,8OAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;4BAEvF,sBACC,8OAAC;gCAAI,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,SAAS,QAAQ;0CACpE,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC;wCAAE,WAAW,CAAC,gEAAgE,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtH;;;;;;kDAKL,8OAAC;wCAAG,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACpH;;;;;;oCAIF,6BACC,8OAAC;wCAAE,WAAW,CAAC,gDAAgD,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtG;;;;;;;;;;;;;;;;;;kCAOT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport Hero from '../../../../components/Hero';\n\ninterface Project {\n  id: string;\n  name: string;\n  description: string;\n  domain: 'Data' | 'EA';\n  country: 'Saudi' | 'Qatar';\n  consultant: string;\n  projectManager: string;\n  status: 'Planning' | 'In Progress' | 'Review' | 'Completed' | 'On Hold';\n  startDate: string;\n  endDate: string;\n  progress: number;\n  createdAt: string;\n}\n\nexport default function ProjectDetail() {\n  const params = useParams();\n  const projectId = params.id as string;\n  \n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [project, setProject] = useState<Project | null>(null);\n  const [activeTab, setActiveTab] = useState<'overview' | 'organization' | 'client-context' | 'project-plan'>('overview');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n\n    // Mock project data\n    const mockProjects: Project[] = [\n      {\n        id: '1',\n        name: 'NDMO Data Governance Implementation',\n        description: 'Implementing comprehensive data governance framework based on NDMO standards for government entities.',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: 'Sarah Johnson',\n        projectManager: 'Mohammed Hassan',\n        status: 'In Progress',\n        startDate: '2024-06-01',\n        endDate: '2024-12-31',\n        progress: 65,\n        createdAt: '2024-05-15'\n      },\n      {\n        id: '2',\n        name: 'Qatar GEA Enterprise Architecture',\n        description: 'Designing and implementing enterprise architecture framework following GEA guidelines for digital transformation.',\n        domain: 'EA',\n        country: 'Qatar',\n        consultant: 'Omar Abdullah',\n        projectManager: 'Fatima Al-Zahra',\n        status: 'Planning',\n        startDate: '2024-09-01',\n        endDate: '2025-03-31',\n        progress: 15,\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '3',\n        name: 'NPC Coding Standards Rollout',\n        description: 'Implementing NPC coding standards and best practices across development teams in Qatar.',\n        domain: 'Data',\n        country: 'Qatar',\n        consultant: 'David Wilson',\n        projectManager: 'Layla Mansouri',\n        status: 'Review',\n        startDate: '2024-04-01',\n        endDate: '2024-10-31',\n        progress: 85,\n        createdAt: '2024-03-15'\n      },\n      {\n        id: '4',\n        name: 'NORA Infrastructure Modernization',\n        description: 'Modernizing national digital infrastructure following NORA framework specifications.',\n        domain: 'EA',\n        country: 'Saudi',\n        consultant: 'Sarah Johnson',\n        projectManager: 'Mohammed Hassan',\n        status: 'Completed',\n        startDate: '2024-01-01',\n        endDate: '2024-07-31',\n        progress: 100,\n        createdAt: '2023-12-01'\n      },\n      {\n        id: '5',\n        name: 'Cross-Border Data Integration',\n        description: 'Establishing secure data integration protocols between Saudi and Qatar government systems.',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: 'Omar Abdullah',\n        projectManager: 'Fatima Al-Zahra',\n        status: 'On Hold',\n        startDate: '2024-08-01',\n        endDate: '2025-02-28',\n        progress: 25,\n        createdAt: '2024-07-10'\n      }\n    ];\n\n    const foundProject = mockProjects.find(p => p.id === projectId);\n    setProject(foundProject || null);\n  }, [projectId]);\n\n  const content = {\n    en: {\n      projectDetails: 'Project Details',\n      backToProjects: 'Back to Projects',\n      overview: 'Overview',\n      organization: 'Organization Structure',\n      clientContext: 'Client Context',\n      projectPlan: 'Project Plan',\n      projectNotFound: 'Project Not Found',\n      projectNotFoundDesc: 'The requested project could not be found.',\n      domain: 'Domain',\n      country: 'Country',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      status: 'Status',\n      progress: 'Progress',\n      startDate: 'Start Date',\n      endDate: 'End Date',\n      dataManagement: 'Data Management',\n      enterpriseArchitecture: 'Enterprise Architecture',\n      saudiArabia: 'Saudi Arabia',\n      qatar: 'Qatar'\n    },\n    ar: {\n      projectDetails: 'تفاصيل المشروع',\n      backToProjects: 'العودة للمشاريع',\n      overview: 'نظرة عامة',\n      organization: 'الهيكل التنظيمي',\n      clientContext: 'سياق العميل',\n      projectPlan: 'خطة المشروع',\n      projectNotFound: 'المشروع غير موجود',\n      projectNotFoundDesc: 'لم يتم العثور على المشروع المطلوب.',\n      domain: 'المجال',\n      country: 'الدولة',\n      consultant: 'المستشار',\n      projectManager: 'مدير المشروع',\n      status: 'الحالة',\n      progress: 'التقدم',\n      startDate: 'تاريخ البداية',\n      endDate: 'تاريخ النهاية',\n      dataManagement: 'إدارة البيانات',\n      enterpriseArchitecture: 'هندسة المؤسسة',\n      saudiArabia: 'المملكة العربية السعودية',\n      qatar: 'قطر'\n    }\n  };\n\n  const getStatusColor = (status: Project['status']) => {\n    switch (status) {\n      case 'Planning': return 'bg-blue-100 text-blue-800';\n      case 'In Progress': return 'bg-yellow-100 text-yellow-800';\n      case 'Review': return 'bg-purple-100 text-purple-800';\n      case 'Completed': return 'bg-green-100 text-green-800';\n      case 'On Hold': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {\n    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';\n  };\n\n  const projectIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  );\n\n  if (!project) {\n    return (\n      <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n        <div className=\"bg-white min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div\n              className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n              style={{ backgroundColor: 'var(--charcoal-grey)' }}\n            >\n              <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].projectNotFound}\n            </h2>\n            <p className={`text-xl text-gray-600 max-w-2xl mx-auto mb-8 ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {content[language].projectNotFoundDesc}\n            </p>\n            <Link\n              href=\"/dashboard/projects\"\n              className={`inline-flex items-center px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n              </svg>\n              {content[language].backToProjects}\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={project.name}\n        subtitle={content[language].projectDetails}\n        description={project.description}\n        icon={projectIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: language === 'en' ? 'Projects' : 'المشاريع', href: '/dashboard/projects' },\n          { label: project.name }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        <div className=\"px-12 py-8\">\n          {/* Back Button */}\n          <div className=\"mb-8\">\n            <Link\n              href=\"/dashboard/projects\"\n              className={`inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n            >\n              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n              </svg>\n              {content[language].backToProjects}\n            </Link>\n          </div>\n\n          {/* Project Summary Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {/* Domain Card */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].domain}</p>\n                  <p className={`text-lg font-bold text-blue-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {project.domain === 'Data' ? content[language].dataManagement : content[language].enterpriseArchitecture}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Country Card */}\n            <div className=\"bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <span className=\"text-lg\">{getCountryFlag(project.country)}</span>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].country}</p>\n                  <p className={`text-lg font-bold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {project.country === 'Saudi' ? content[language].saudiArabia : content[language].qatar}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Status Card */}\n            <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].status}</p>\n                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)} ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {project.status}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Progress Card */}\n            <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-orange-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].progress}</p>\n                  <p className={`text-lg font-bold text-orange-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{project.progress}%</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Tab Navigation */}\n          <div className=\"border-b border-gray-200 mb-8\">\n            <nav className={`flex space-x-8 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {[\n                { key: 'overview', label: content[language].overview, icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },\n                { key: 'organization', label: content[language].organization, icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },\n                { key: 'client-context', label: content[language].clientContext, icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2' },\n                { key: 'project-plan', label: content[language].projectPlan, icon: 'M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01' }\n              ].map((tab) => (\n                <button\n                  key={tab.key}\n                  onClick={() => setActiveTab(tab.key as any)}\n                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.key\n                      ? 'border-emerald-500 text-emerald-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                >\n                  <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={tab.icon} />\n                  </svg>\n                  {tab.label}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"min-h-[600px]\">\n            {activeTab === 'overview' && (\n              <div className=\"space-y-8\">\n                {/* Project Information */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    Project Information\n                  </h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].consultant}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.consultant}</p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].projectManager}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.projectManager}</p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].startDate}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.startDate}</p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].endDate}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.endDate}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Progress Visualization */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    Progress Overview\n                  </h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`text-lg font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        Overall Progress\n                      </span>\n                      <span className={`text-lg font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>\n                        {project.progress}%\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-4\">\n                      <div\n                        className=\"h-4 rounded-full transition-all duration-300\"\n                        style={{\n                          width: `${project.progress}%`,\n                          background: 'linear-gradient(90deg, var(--emerald-green), var(--deep-emerald))'\n                        }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'organization' && (\n              <div className=\"text-center py-16\">\n                <div\n                  className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? 'Organization Structure' : 'الهيكل التنظيمي'}\n                </h3>\n                <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en'\n                    ? 'Organization structure visualization with import from Excel capability coming soon.'\n                    : 'تصور الهيكل التنظيمي مع إمكانية الاستيراد من Excel قريباً.'\n                  }\n                </p>\n              </div>\n            )}\n\n            {activeTab === 'client-context' && (\n              <div className=\"text-center py-16\">\n                <div\n                  className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2\" />\n                  </svg>\n                </div>\n                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? 'Client Context' : 'سياق العميل'}\n                </h3>\n                <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en'\n                    ? 'Client context management with culture, politics, and stakeholder information coming soon.'\n                    : 'إدارة سياق العميل مع معلومات الثقافة والسياسة وأصحاب المصلحة قريباً.'\n                  }\n                </p>\n              </div>\n            )}\n\n            {activeTab === 'project-plan' && (\n              <div className=\"text-center py-16\">\n                <div\n                  className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n                  </svg>\n                </div>\n                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? 'Project Plan' : 'خطة المشروع'}\n                </h3>\n                <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en'\n                    ? 'Advanced project planning with Gantt charts and dependency management coming soon.'\n                    : 'تخطيط المشاريع المتقدم مع مخططات جانت وإدارة التبعيات قريباً.'\n                  }\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,EAAE;IAE3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmE;IAE5G,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;QAEZ,oBAAoB;QACpB,MAAM,eAA0B;YAC9B;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,WAAW;YACb;SACD;QAED,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,WAAW,gBAAgB;IAC7B,GAAG;QAAC;KAAU;IAEd,MAAM,UAAU;QACd,IAAI;YACF,gBAAgB;YAChB,gBAAgB;YAChB,UAAU;YACV,cAAc;YACd,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,UAAU;YACV,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;QACT;QACA,IAAI;YACF,gBAAgB;YAChB,gBAAgB;YAChB,UAAU;YACV,cAAc;YACd,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,UAAU;YACV,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,YAAY,UAAU,SAAS;IACxC;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;sBACjE,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAAuB;sCAEjD,cAAA,8OAAC;gCAAI,WAAU;gCAAY,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACnE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4BAAE,OAAO;gCAAE,OAAO;4BAAuB;sCACxH,OAAO,CAAC,SAAS,CAAC,eAAe;;;;;;sCAEpC,8OAAC;4BAAE,WAAW,CAAC,6CAA6C,EAAE,aAAa,OAAO,gBAAgB,IAAI;sCACnG,OAAO,CAAC,SAAS,CAAC,mBAAmB;;;;;;sCAExC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,kJAAkJ,EAAE,aAAa,OAAO,iCAAiC,IAAI;4BACzN,OAAO;gCAAE,iBAAiB;4BAAuB;;8CAEjD,8OAAC;oCAAI,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;oCAAE,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACrH,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCAEtE,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BACjE,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAO,QAAQ,IAAI;gBACnB,UAAU,OAAO,CAAC,SAAS,CAAC,cAAc;gBAC1C,aAAa,QAAQ,WAAW;gBAChC,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,aAAa,OAAO,aAAa;wBAAY,MAAM;oBAAsB;oBAClF;wBAAE,OAAO,QAAQ,IAAI;oBAAC;iBACvB;;;;;;0BAGH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,CAAC,6EAA6E,EAAE,aAAa,OAAO,iCAAiC,IAAI;;kDAEpJ,8OAAC;wCAAI,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;wCAAE,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACrH,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCAEtE,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;sCAKrC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,IAAI;;0DAChF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,SAAS,QAAQ;;kEACtD,8OAAC;wDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAAG,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAC1G,8OAAC;wDAAE,WAAW,CAAC,gCAAgC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEACtF,QAAQ,MAAM,KAAK,SAAS,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;8CAOhH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,IAAI;;0DAChF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAW,eAAe,QAAQ,OAAO;;;;;;;;;;;0DAE3D,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,SAAS,QAAQ;;kEACtD,8OAAC;wDAAE,WAAW,CAAC,yBAAyB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAAG,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;kEAC9G,8OAAC;wDAAE,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEACzF,QAAQ,OAAO,KAAK,UAAU,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;8CAO9F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,IAAI;;0DAChF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,SAAS,QAAQ;;kEACtD,8OAAC;wDAAE,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAAG,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAC5G,8OAAC;wDAAK,WAAW,CAAC,oEAAoE,EAAE,eAAe,QAAQ,MAAM,EAAE,CAAC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC/J,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAOvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,IAAI;;0DAChF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,SAAS,QAAQ;;kEACtD,8OAAC;wDAAE,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAAG,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kEAC9G,8OAAC;wDAAE,WAAW,CAAC,kCAAkC,EAAE,aAAa,OAAO,gBAAgB,IAAI;;4DAAG,QAAQ,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAC,eAAe,EAAE,aAAa,OAAO,qCAAqC,IAAI;0CAC5F;oCACC;wCAAE,KAAK;wCAAY,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ;wCAAE,MAAM;oCAAuM;oCACnQ;wCAAE,KAAK;wCAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,YAAY;wCAAE,MAAM;oCAAyQ;oCAC7U;wCAAE,KAAK;wCAAkB,OAAO,OAAO,CAAC,SAAS,CAAC,aAAa;wCAAE,MAAM;oCAAqL;oCAC5P;wCAAE,KAAK;wCAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW;wCAAE,MAAM;oCAAiK;iCACrO,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,GAAG;wCACnC,WAAW,CAAC,6EAA6E,EACvF,cAAc,IAAI,GAAG,GACjB,wCACA,6EACL,CAAC,EAAE,aAAa,OAAO,iCAAiC,IAAI;;0DAE7D,8OAAC;gDAAI,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,SAAS,QAAQ;gDAAE,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC1G,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAG,IAAI,IAAI;;;;;;;;;;;4CAE/E,IAAI,KAAK;;uCAXL,IAAI,GAAG;;;;;;;;;;;;;;;sCAkBpB,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,4BACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oDAAE,OAAO;wDAAE,OAAO;oDAAuB;8DAAG;;;;;;8DAG9H,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,gBAAgB,IAAI;oEAAE,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;8EAE/B,8OAAC;oEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;8EAAG,QAAQ,UAAU;;;;;;;;;;;;sEAExF,8OAAC;;8EACC,8OAAC;oEAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,gBAAgB,IAAI;oEAAE,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;8EAEnC,8OAAC;oEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;8EAAG,QAAQ,cAAc;;;;;;;;;;;;sEAE5F,8OAAC;;8EACC,8OAAC;oEAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,gBAAgB,IAAI;oEAAE,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;8EAE9B,8OAAC;oEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;8EAAG,QAAQ,SAAS;;;;;;;;;;;;sEAEvF,8OAAC;;8EACC,8OAAC;oEAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,gBAAgB,IAAI;oEAAE,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;8EAE5B,8OAAC;oEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;8EAAG,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sDAMzF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oDAAE,OAAO;wDAAE,OAAO;oDAAuB;8DAAG;;;;;;8DAG9H,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAC,oBAAoB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oEAAE,OAAO;wEAAE,OAAO;oEAAuB;8EAAG;;;;;;8EAG5H,8OAAC;oEAAK,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oEAAE,OAAO;wEAAE,OAAO;oEAAuB;;wEACpH,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;sEAGtB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,GAAG,QAAQ,QAAQ,CAAC,CAAC,CAAC;oEAC7B,YAAY;gEACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQX,cAAc,gCACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAuB;sDAEjD,cAAA,8OAAC;gDAAI,WAAU;gDAAY,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACxH,aAAa,OAAO,2BAA2B;;;;;;sDAElD,8OAAC;4CAAE,WAAW,CAAC,wCAAwC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDAC9F,aAAa,OACV,wFACA;;;;;;;;;;;;gCAMT,cAAc,kCACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAuB;sDAEjD,cAAA,8OAAC;gDAAI,WAAU;gDAAY,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACxH,aAAa,OAAO,mBAAmB;;;;;;sDAE1C,8OAAC;4CAAE,WAAW,CAAC,wCAAwC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDAC9F,aAAa,OACV,+FACA;;;;;;;;;;;;gCAMT,cAAc,gCACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAuB;sDAEjD,cAAA,8OAAC;gDAAI,WAAU;gDAAY,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACxH,aAAa,OAAO,iBAAiB;;;;;;sDAExC,8OAAC;4CAAE,WAAW,CAAC,wCAAwC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDAC9F,aAAa,OACV,uFACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB", "debugId": null}}]}