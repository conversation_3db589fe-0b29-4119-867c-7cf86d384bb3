{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/training/%5Bcertification%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport Hero from '../../../../components/Hero';\n\ninterface Question {\n  id: string;\n  question: string;\n  questionAr: string;\n  options: string[];\n  optionsAr: string[];\n  correctAnswer: number;\n  difficulty: 'easy' | 'medium' | 'hard';\n}\n\ninterface AssignedPerson {\n  id: string;\n  name: string;\n  email: string;\n  role: string;\n  progress: number;\n  status: 'not-started' | 'in-progress' | 'completed';\n  assignedDate: string;\n}\n\ninterface Certification {\n  id: string;\n  name: string;\n  nameAr: string;\n  description: string;\n  descriptionAr: string;\n  icon: string;\n  color: string;\n}\n\nexport default function CertificationDetail() {\n  const params = useParams();\n  const certificationId = params.certification as string;\n  \n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [activeTab, setActiveTab] = useState<'questions' | 'assignments'>('questions');\n  const [questions, setQuestions] = useState<Question[]>([]);\n  const [assignedPeople, setAssignedPeople] = useState<AssignedPerson[]>([]);\n  const [showImportModal, setShowImportModal] = useState(false);\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n    \n    // Load mock data\n    loadMockData();\n  }, [certificationId]);\n\n  const loadMockData = () => {\n    // Mock questions data\n    const mockQuestions: Question[] = [\n      {\n        id: '1',\n        question: 'What is the primary purpose of enterprise architecture?',\n        questionAr: 'ما هو الغرض الأساسي من الهندسة المعمارية للمؤسسات؟',\n        options: [\n          'To design software applications',\n          'To align business and IT strategy',\n          'To manage databases',\n          'To create user interfaces'\n        ],\n        optionsAr: [\n          'لتصميم تطبيقات البرمجيات',\n          'لمواءمة استراتيجية الأعمال وتكنولوجيا المعلومات',\n          'لإدارة قواعد البيانات',\n          'لإنشاء واجهات المستخدم'\n        ],\n        correctAnswer: 1,\n        difficulty: 'medium'\n      },\n      {\n        id: '2',\n        question: 'Which phase comes first in TOGAF ADM?',\n        questionAr: 'أي مرحلة تأتي أولاً في TOGAF ADM؟',\n        options: [\n          'Architecture Vision',\n          'Business Architecture',\n          'Preliminary Phase',\n          'Technology Architecture'\n        ],\n        optionsAr: [\n          'رؤية الهندسة المعمارية',\n          'هندسة الأعمال',\n          'المرحلة التمهيدية',\n          'هندسة التكنولوجيا'\n        ],\n        correctAnswer: 2,\n        difficulty: 'easy'\n      }\n    ];\n\n    // Mock assigned people data\n    const mockAssigned: AssignedPerson[] = [\n      {\n        id: '1',\n        name: 'Ahmed Al-Rashid',\n        email: '<EMAIL>',\n        role: 'Enterprise Architect',\n        progress: 75,\n        status: 'in-progress',\n        assignedDate: '2024-01-15'\n      },\n      {\n        id: '2',\n        name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'Solution Architect',\n        progress: 100,\n        status: 'completed',\n        assignedDate: '2024-01-10'\n      },\n      {\n        id: '3',\n        name: 'Mohammed Hassan',\n        email: '<EMAIL>',\n        role: 'Business Analyst',\n        progress: 0,\n        status: 'not-started',\n        assignedDate: '2024-01-20'\n      }\n    ];\n\n    setQuestions(mockQuestions);\n    setAssignedPeople(mockAssigned);\n  };\n\n  const certifications: Record<string, Certification> = {\n    togaf: {\n      id: 'togaf',\n      name: 'TOGAF',\n      nameAr: 'توجاف',\n      description: 'The Open Group Architecture Framework - Enterprise Architecture certification',\n      descriptionAr: 'إطار عمل مجموعة المفتوح للهندسة المعمارية - شهادة الهندسة المعمارية للمؤسسات',\n      icon: '🏗️',\n      color: 'from-blue-500 to-blue-600'\n    },\n    cdmp: {\n      id: 'cdmp',\n      name: 'CDMP',\n      nameAr: 'سي دي إم بي',\n      description: 'Certified Data Management Professional - Data governance and management',\n      descriptionAr: 'محترف إدارة البيانات المعتمد - حوكمة وإدارة البيانات',\n      icon: '📊',\n      color: 'from-green-500 to-green-600'\n    },\n    cobit: {\n      id: 'cobit',\n      name: 'COBIT',\n      nameAr: 'كوبيت',\n      description: 'Control Objectives for Information and Related Technologies',\n      descriptionAr: 'أهداف التحكم في المعلومات والتقنيات ذات الصلة',\n      icon: '🔒',\n      color: 'from-purple-500 to-purple-600'\n    },\n    cipp: {\n      id: 'cipp',\n      name: 'CIPP',\n      nameAr: 'سي آي بي بي',\n      description: 'Certified Information Privacy Professional - Privacy and data protection',\n      descriptionAr: 'محترف خصوصية المعلومات المعتمد - الخصوصية وحماية البيانات',\n      icon: '🛡️',\n      color: 'from-red-500 to-red-600'\n    }\n  };\n\n  const currentCert = certifications[certificationId];\n\n  const content = {\n    en: {\n      questionBank: 'Question Bank',\n      assignments: 'Assignments',\n      importQuestions: 'Import Questions',\n      addQuestion: 'Add Question',\n      assignPeople: 'Assign People',\n      totalQuestions: 'Total Questions',\n      assignedPeople: 'Assigned People',\n      difficulty: 'Difficulty',\n      easy: 'Easy',\n      medium: 'Medium',\n      hard: 'Hard',\n      name: 'Name',\n      email: 'Email',\n      role: 'Role',\n      progress: 'Progress',\n      status: 'Status',\n      assignedDate: 'Assigned Date',\n      notStarted: 'Not Started',\n      inProgress: 'In Progress',\n      completed: 'Completed',\n      importFromExcel: 'Import from Excel',\n      selectFile: 'Select Excel File',\n      uploadFile: 'Upload File'\n    },\n    ar: {\n      questionBank: 'بنك الأسئلة',\n      assignments: 'التكليفات',\n      importQuestions: 'استيراد الأسئلة',\n      addQuestion: 'إضافة سؤال',\n      assignPeople: 'تعيين الأشخاص',\n      totalQuestions: 'إجمالي الأسئلة',\n      assignedPeople: 'الأشخاص المعينين',\n      difficulty: 'الصعوبة',\n      easy: 'سهل',\n      medium: 'متوسط',\n      hard: 'صعب',\n      name: 'الاسم',\n      email: 'البريد الإلكتروني',\n      role: 'الدور',\n      progress: 'التقدم',\n      status: 'الحالة',\n      assignedDate: 'تاريخ التعيين',\n      notStarted: 'لم يبدأ',\n      inProgress: 'قيد التقدم',\n      completed: 'مكتمل',\n      importFromExcel: 'استيراد من إكسل',\n      selectFile: 'اختر ملف إكسل',\n      uploadFile: 'رفع الملف'\n    }\n  };\n\n  if (!currentCert) {\n    return <div>Certification not found</div>;\n  }\n\n  const certIcon = (\n    <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${currentCert.color} flex items-center justify-center text-white text-lg`}>\n      {currentCert.icon}\n    </div>\n  );\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy': return 'bg-green-100 text-green-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'hard': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-100 text-green-800';\n      case 'in-progress': return 'bg-blue-100 text-blue-800';\n      case 'not-started': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={language === 'en' ? currentCert.name : currentCert.nameAr}\n        subtitle={language === 'en' ? 'Certification Management' : 'إدارة الشهادات'}\n        description={language === 'en' ? currentCert.description : currentCert.descriptionAr}\n        icon={certIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: language === 'en' ? 'Training' : 'التدريب', href: '/dashboard/training' },\n          { label: language === 'en' ? currentCert.name : currentCert.nameAr }\n        ]}\n      />\n\n      <div className=\"bg-gray-50 min-h-screen\">\n        <div className=\"px-12 py-8\">\n          {/* Statistics Cards */}\n          <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 mb-8`}>\n            <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {questions.length}\n                  </p>\n                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].totalQuestions}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {assignedPeople.length}\n                  </p>\n                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].assignedPeople}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {Math.round(assignedPeople.reduce((acc, person) => acc + person.progress, 0) / assignedPeople.length) || 0}%\n                  </p>\n                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].progress}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Tab Navigation */}\n          <div className=\"border-b border-gray-200 mb-8\">\n            <nav className={`flex space-x-8 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {[\n                {\n                  key: 'questions',\n                  label: content[language].questionBank,\n                  icon: 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'\n                },\n                {\n                  key: 'assignments',\n                  label: content[language].assignments,\n                  icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'\n                }\n              ].map((tab) => (\n                <button\n                  key={tab.key}\n                  onClick={() => setActiveTab(tab.key as any)}\n                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.key\n                      ? 'border-emerald-500 text-emerald-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                >\n                  <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={tab.icon} />\n                  </svg>\n                  {tab.label}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"min-h-[600px]\">\n            {activeTab === 'questions' && (\n              <div className=\"space-y-6\">\n                {/* Question Bank Header */}\n                <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                  <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {content[language].questionBank}\n                  </h3>\n                  <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                    <button\n                      onClick={() => setShowImportModal(true)}\n                      className={`px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                    >\n                      <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                      </svg>\n                      {content[language].importQuestions}\n                    </button>\n                    <button\n                      className={`px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                    >\n                      <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                      </svg>\n                      {content[language].addQuestion}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Questions List */}\n                <div className=\"space-y-4\">\n                  {questions.map((question, index) => (\n                    <div key={question.id} className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\n                      <div className={`flex items-start justify-between ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                        <div className=\"flex-1\">\n                          <div className={`flex items-center mb-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                            <span className={`text-lg font-bold text-gray-400 ${language === 'ar' ? 'ml-4' : 'mr-4'}`}>\n                              Q{index + 1}\n                            </span>\n                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty)}`}>\n                              {content[language][question.difficulty as keyof typeof content['en']]}\n                            </span>\n                          </div>\n                          <h4 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                            {language === 'en' ? question.question : question.questionAr}\n                          </h4>\n                          <div className=\"space-y-2\">\n                            {(language === 'en' ? question.options : question.optionsAr).map((option, optionIndex) => (\n                              <div\n                                key={optionIndex}\n                                className={`flex items-center p-3 rounded-lg ${\n                                  optionIndex === question.correctAnswer\n                                    ? 'bg-green-50 border border-green-200'\n                                    : 'bg-gray-50 border border-gray-200'\n                                } ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}\n                              >\n                                <span className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${\n                                  optionIndex === question.correctAnswer\n                                    ? 'bg-green-500 text-white'\n                                    : 'bg-gray-300 text-gray-600'\n                                } ${language === 'ar' ? 'ml-3' : 'mr-3'}`}>\n                                  {String.fromCharCode(65 + optionIndex)}\n                                </span>\n                                <span className={`${language === 'ar' ? 'font-arabic' : ''}`}>\n                                  {option}\n                                </span>\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                        <div className={`flex space-x-2 ${language === 'ar' ? 'flex-row-reverse space-x-reverse ml-4' : 'mr-4'}`}>\n                          <button className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\">\n                            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                            </svg>\n                          </button>\n                          <button className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\">\n                            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                            </svg>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'assignments' && (\n              <div className=\"space-y-6\">\n                {/* Assignments Header */}\n                <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                  <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {content[language].assignments}\n                  </h3>\n                  <button\n                    className={`px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                  >\n                    <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                    </svg>\n                    {content[language].assignPeople}\n                  </button>\n                </div>\n\n                {/* Assignments Table */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden\">\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"w-full\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                            {content[language].name}\n                          </th>\n                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                            {content[language].email}\n                          </th>\n                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                            {content[language].role}\n                          </th>\n                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                            {content[language].progress}\n                          </th>\n                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                            {content[language].status}\n                          </th>\n                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                            {content[language].assignedDate}\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {assignedPeople.map((person) => (\n                          <tr key={person.id} className=\"hover:bg-gray-50\">\n                            <td className={`px-6 py-4 whitespace-nowrap ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                              <div className=\"flex items-center\">\n                                <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center text-white font-medium\">\n                                  {person.name.charAt(0)}\n                                </div>\n                                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                                  <div className={`text-sm font-medium text-gray-900 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                    {person.name}\n                                  </div>\n                                </div>\n                              </div>\n                            </td>\n                            <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                              {person.email}\n                            </td>\n                            <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                              {person.role}\n                            </td>\n                            <td className={`px-6 py-4 whitespace-nowrap ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                              <div className=\"flex items-center\">\n                                <div className=\"w-full bg-gray-200 rounded-full h-2 mr-3\">\n                                  <div\n                                    className=\"bg-gradient-to-r from-emerald-500 to-emerald-600 h-2 rounded-full transition-all duration-300\"\n                                    style={{ width: `${person.progress}%` }}\n                                  ></div>\n                                </div>\n                                <span className=\"text-sm font-medium text-gray-900\">\n                                  {person.progress}%\n                                </span>\n                              </div>\n                            </td>\n                            <td className={`px-6 py-4 whitespace-nowrap ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(person.status)}`}>\n                                {content[language][person.status.replace('-', '') as keyof typeof content['en']]}\n                              </span>\n                            </td>\n                            <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                              {new Date(person.assignedDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Import Modal */}\n          {showImportModal && (\n            <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n              <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4\">\n                <div className=\"p-6\">\n                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <h3 className={`text-xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      {content[language].importFromExcel}\n                    </h3>\n                    <button\n                      onClick={() => setShowImportModal(false)}\n                      className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                    >\n                      <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                      </svg>\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].selectFile}\n                      </label>\n                      <div className=\"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-emerald-400 transition-colors\">\n                        <svg className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                        </svg>\n                        <p className={`text-sm text-gray-600 mb-2 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                          {language === 'en' ? 'Drop your Excel file here or click to browse' : 'اسقط ملف الإكسل هنا أو انقر للتصفح'}\n                        </p>\n                        <input\n                          type=\"file\"\n                          accept=\".xlsx,.xls\"\n                          className=\"hidden\"\n                          id=\"excel-upload\"\n                        />\n                        <label\n                          htmlFor=\"excel-upload\"\n                          className=\"cursor-pointer inline-flex items-center px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors\"\n                        >\n                          {content[language].selectFile}\n                        </label>\n                      </div>\n                    </div>\n\n                    <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                      <button\n                        onClick={() => setShowImportModal(false)}\n                        className={`flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors ${language === 'ar' ? 'font-arabic' : ''}`}\n                      >\n                        {language === 'en' ? 'Cancel' : 'إلغاء'}\n                      </button>\n                      <button\n                        className={`flex-1 px-4 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-200 ${language === 'ar' ? 'font-arabic' : ''}`}\n                      >\n                        {content[language].uploadFile}\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAoCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,OAAO,aAAa;IAE5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;YAEZ,iBAAiB;YACjB;QACF;wCAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe;QACnB,sBAAsB;QACtB,MAAM,gBAA4B;YAChC;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT;oBACA;oBACA;oBACA;iBACD;gBACD,eAAe;gBACf,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT;oBACA;oBACA;oBACA;iBACD;gBACD,eAAe;gBACf,YAAY;YACd;SACD;QAED,4BAA4B;QAC5B,MAAM,eAAiC;YACrC;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,cAAc;YAChB;SACD;QAED,aAAa;QACb,kBAAkB;IACpB;IAEA,MAAM,iBAAgD;QACpD,OAAO;YACL,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,eAAe;YACf,MAAM;YACN,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,eAAe;YACf,MAAM;YACN,OAAO;QACT;QACA,OAAO;YACL,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,eAAe;YACf,MAAM;YACN,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,eAAe;YACf,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,cAAc,cAAc,CAAC,gBAAgB;IAEnD,MAAM,UAAU;QACd,IAAI;YACF,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,iBAAiB;YACjB,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,iBAAiB;YACjB,YAAY;YACZ,YAAY;QACd;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAEA,MAAM,yBACJ,6LAAC;QAAI,WAAW,AAAC,wCAAyD,OAAlB,YAAY,KAAK,EAAC;kBACvE,YAAY,IAAI;;;;;;IAIrB,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BACpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,aAAa,OAAO,YAAY,IAAI,GAAG,YAAY,MAAM;gBAChE,UAAU,aAAa,OAAO,6BAA6B;gBAC3D,aAAa,aAAa,OAAO,YAAY,WAAW,GAAG,YAAY,aAAa;gBACpF,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,aAAa,OAAO,aAAa;wBAAW,MAAM;oBAAsB;oBACjF;wBAAE,OAAO,aAAa,OAAO,YAAY,IAAI,GAAG,YAAY,MAAM;oBAAC;iBACpE;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAY;;8CACf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;kEACzD,6LAAC;wDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAClH,UAAU,MAAM;;;;;;kEAEnB,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAMzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;kEACzD,6LAAC;wDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAClH,eAAe,MAAM;;;;;;kEAExB,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAMzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;kEACzD,6LAAC;wDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;;4DAClH,KAAK,KAAK,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,EAAE,KAAK,eAAe,MAAM,KAAK;4DAAE;;;;;;;kEAE7G,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;0CACxF;oCACC;wCACE,KAAK;wCACL,OAAO,OAAO,CAAC,SAAS,CAAC,YAAY;wCACrC,MAAM;oCACR;oCACA;wCACE,KAAK;wCACL,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW;wCACpC,MAAM;oCACR;iCACD,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,GAAG;wCACnC,WAAW,AAAC,gFAIR,OAHF,cAAc,IAAI,GAAG,GACjB,wCACA,8EACL,KAA2D,OAAxD,aAAa,OAAO,iCAAiC;;0DAEzD,6LAAC;gDAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC1G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAG,IAAI,IAAI;;;;;;;;;;;4CAE/E,IAAI,KAAK;;uCAXL,IAAI,GAAG;;;;;;;;;;;;;;;sCAkBpB,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,6BACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAW,AAAC,qCAAwF,OAApD,aAAa,OAAO,qBAAqB;;8DAC5F,6LAAC;oDAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DACnH,OAAO,CAAC,SAAS,CAAC,YAAY;;;;;;8DAEjC,6LAAC;oDAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;;sEACzF,6LAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAW,AAAC,4LAAmP,OAAxD,aAAa,OAAO,iCAAiC;;8EAE5P,6LAAC;oEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;oEAAU,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC1G,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEAEtE,OAAO,CAAC,SAAS,CAAC,eAAe;;;;;;;sEAEpC,6LAAC;4DACC,WAAW,AAAC,wMAA+P,OAAxD,aAAa,OAAO,iCAAiC;;8EAExQ,6LAAC;oEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;oEAAU,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC1G,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEAEtE,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;;sDAMpC,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;oDAAsB,WAAU;8DAC/B,cAAA,6LAAC;wDAAI,WAAW,AAAC,oCAAuF,OAApD,aAAa,OAAO,qBAAqB;;0EAC3F,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;0FACjF,6LAAC;gFAAK,WAAW,AAAC,mCAAsE,OAApC,aAAa,OAAO,SAAS;;oFAAU;oFACvF,QAAQ;;;;;;;0FAEZ,6LAAC;gFAAK,WAAW,AAAC,8CAAqF,OAAxC,mBAAmB,SAAS,UAAU;0FAClG,OAAO,CAAC,SAAS,CAAC,SAAS,UAAU,CAA+B;;;;;;;;;;;;kFAGzE,6LAAC;wEAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wEAAM,OAAO;4EAAE,OAAO;wEAAuB;kFAC3H,aAAa,OAAO,SAAS,QAAQ,GAAG,SAAS,UAAU;;;;;;kFAE9D,6LAAC;wEAAI,WAAU;kFACZ,CAAC,aAAa,OAAO,SAAS,OAAO,GAAG,SAAS,SAAS,EAAE,GAAG,CAAC,CAAC,QAAQ,4BACxE,6LAAC;gFAEC,WAAW,AAAC,oCAIR,OAHF,gBAAgB,SAAS,aAAa,GAClC,wCACA,qCACL,KAAuD,OAApD,aAAa,OAAO,qBAAqB;;kGAE7C,6LAAC;wFAAK,WAAW,AAAC,6EAId,OAHF,gBAAgB,SAAS,aAAa,GAClC,4BACA,6BACL,KAAuC,OAApC,aAAa,OAAO,SAAS;kGAC9B,OAAO,YAAY,CAAC,KAAK;;;;;;kGAE5B,6LAAC;wFAAK,WAAW,AAAC,GAAyC,OAAvC,aAAa,OAAO,gBAAgB;kGACrD;;;;;;;+EAfE;;;;;;;;;;;;;;;;0EAqBb,6LAAC;gEAAI,WAAW,AAAC,kBAAsF,OAArE,aAAa,OAAO,0CAA0C;;kFAC9F,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA9CrE,SAAS,EAAE;;;;;;;;;;;;;;;;gCAyD5B,cAAc,+BACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAW,AAAC,qCAAwF,OAApD,aAAa,OAAO,qBAAqB;;8DAC5F,6LAAC;oDAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DACnH,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;8DAEhC,6LAAC;oDACC,WAAW,AAAC,wMAA+P,OAAxD,aAAa,OAAO,iCAAiC;;sEAExQ,6LAAC;4DAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC1G,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,OAAO,CAAC,SAAS,CAAC,YAAY;;;;;;;;;;;;;sDAKnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAM,WAAU;sEACf,cAAA,6LAAC;;kFACC,6LAAC;wEAAG,WAAW,AAAC,kFAAmI,OAAlD,aAAa,OAAO,2BAA2B;kFAC7I,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;kFAEzB,6LAAC;wEAAG,WAAW,AAAC,kFAAmI,OAAlD,aAAa,OAAO,2BAA2B;kFAC7I,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;kFAE1B,6LAAC;wEAAG,WAAW,AAAC,kFAAmI,OAAlD,aAAa,OAAO,2BAA2B;kFAC7I,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;kFAEzB,6LAAC;wEAAG,WAAW,AAAC,kFAAmI,OAAlD,aAAa,OAAO,2BAA2B;kFAC7I,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kFAE7B,6LAAC;wEAAG,WAAW,AAAC,kFAAmI,OAAlD,aAAa,OAAO,2BAA2B;kFAC7I,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kFAE3B,6LAAC;wEAAG,WAAW,AAAC,kFAAmI,OAAlD,aAAa,OAAO,2BAA2B;kFAC7I,OAAO,CAAC,SAAS,CAAC,YAAY;;;;;;;;;;;;;;;;;sEAIrC,6LAAC;4DAAM,WAAU;sEACd,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;oEAAmB,WAAU;;sFAC5B,6LAAC;4EAAG,WAAW,AAAC,+BAA6E,OAA/C,aAAa,OAAO,eAAe;sFAC/E,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGACZ,OAAO,IAAI,CAAC,MAAM,CAAC;;;;;;kGAEtB,6LAAC;wFAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;kGAC9C,cAAA,6LAAC;4FAAI,WAAW,AAAC,qCAA2E,OAAvC,aAAa,OAAO,gBAAgB;sGACtF,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;sFAKpB,6LAAC;4EAAG,WAAW,AAAC,qDAAmG,OAA/C,aAAa,OAAO,eAAe;sFACpG,OAAO,KAAK;;;;;;sFAEf,6LAAC;4EAAG,WAAW,AAAC,qDAA+G,OAA3D,aAAa,OAAO,2BAA2B;sFAChH,OAAO,IAAI;;;;;;sFAEd,6LAAC;4EAAG,WAAW,AAAC,+BAA6E,OAA/C,aAAa,OAAO,eAAe;sFAC/E,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC;4FACC,WAAU;4FACV,OAAO;gGAAE,OAAO,AAAC,GAAkB,OAAhB,OAAO,QAAQ,EAAC;4FAAG;;;;;;;;;;;kGAG1C,6LAAC;wFAAK,WAAU;;4FACb,OAAO,QAAQ;4FAAC;;;;;;;;;;;;;;;;;;sFAIvB,6LAAC;4EAAG,WAAW,AAAC,+BAA6E,OAA/C,aAAa,OAAO,eAAe;sFAC/E,cAAA,6LAAC;gFAAK,WAAW,AAAC,8CAA2E,OAA9B,eAAe,OAAO,MAAM;0FACxF,OAAO,CAAC,SAAS,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,IAAkC;;;;;;;;;;;sFAGpF,6LAAC;4EAAG,WAAW,AAAC,qDAAmG,OAA/C,aAAa,OAAO,eAAe;sFACpG,IAAI,KAAK,OAAO,YAAY,EAAE,kBAAkB,CAAC,aAAa,OAAO,UAAU;;;;;;;mEAtC3E,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAmDjC,iCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,0CAA6F,OAApD,aAAa,OAAO,qBAAqB;;8DACjG,6LAAC;oDAAG,WAAW,AAAC,qBAA2D,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DAClH,OAAO,CAAC,SAAS,CAAC,eAAe;;;;;;8DAEpC,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sDAK3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAW,AAAC,kCAA4F,OAA3D,aAAa,OAAO,2BAA2B;4DAAe,OAAO;gEAAE,OAAO;4DAAuB;sEACtJ,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;oEAAuC,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC9F,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;8EAEvE,6LAAC;oEAAE,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;8EAC7E,aAAa,OAAO,iDAAiD;;;;;;8EAExE,6LAAC;oEACC,MAAK;oEACL,QAAO;oEACP,WAAU;oEACV,IAAG;;;;;;8EAEL,6LAAC;oEACC,SAAQ;oEACR,WAAU;8EAET,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;;;;;;;;;;;;;8DAKnC,6LAAC;oDAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;;sEACzF,6LAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAW,AAAC,uGAA6I,OAAvC,aAAa,OAAO,gBAAgB;sEAErJ,aAAa,OAAO,WAAW;;;;;;sEAElC,6LAAC;4DACC,WAAW,AAAC,mKAAyM,OAAvC,aAAa,OAAO,gBAAgB;sEAEjN,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrD;GAjkBwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}