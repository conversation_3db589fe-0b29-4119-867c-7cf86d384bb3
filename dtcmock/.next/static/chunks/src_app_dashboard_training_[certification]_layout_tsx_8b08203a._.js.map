{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/training/%5Bcertification%5D/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Sidebar from '../../../../components/Sidebar';\n\nexport default function CertificationLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  // Mock: Get user data from localStorage or context\n  useEffect(() => {\n    // In a real app, this would come from authentication context or API\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const handleLanguageChange = (newLanguage: 'en' | 'ar') => {\n    setLanguage(newLanguage);\n    localStorage.setItem('language', newLanguage);\n  };\n\n  return (\n    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Sidebar */}\n      <Sidebar\n        userRole={userRole}\n        language={language}\n        onLanguageChange={handleLanguageChange}\n      />\n\n      {/* Main Content - No gaps, seamless */}\n      <div className={`sidebar-transition ${language === 'ar' ? 'mr-72' : 'ml-72'}`}>\n        <main className=\"dashboard-content min-h-screen bg-gray-50\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,oBAAoB,KAI3C;QAJ2C,EAC1C,QAAQ,EAGT,GAJ2C;;IAK1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,oEAAoE;YACpE,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;YACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YAExE,YAAY;YACZ,YAAY;QACd;wCAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,gBAAiD,OAAlC,aAAa,OAAO,QAAQ;QAAS,KAAK,aAAa,OAAO,QAAQ;;0BAEpG,6LAAC,gIAAA,CAAA,UAAO;gBACN,UAAU;gBACV,UAAU;gBACV,kBAAkB;;;;;;0BAIpB,6LAAC;gBAAI,WAAW,AAAC,sBAA2D,OAAtC,aAAa,OAAO,UAAU;0BAClE,cAAA,6LAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX;GAxCwB;KAAA", "debugId": null}}]}