'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Hero from '../../../../components/Hero';

interface Question {
  id: string;
  question: string;
  questionAr: string;
  options: string[];
  optionsAr: string[];
  correctAnswer: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface AssignedPerson {
  id: string;
  name: string;
  email: string;
  role: string;
  progress: number;
  status: 'not-started' | 'in-progress' | 'completed';
  assignedDate: string;
}

interface Certification {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  icon: string;
  color: string;
}

export default function CertificationDetail() {
  const params = useParams();
  const certificationId = params.certification as string;
  
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [activeTab, setActiveTab] = useState<'questions' | 'assignments'>('questions');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [assignedPeople, setAssignedPeople] = useState<AssignedPerson[]>([]);
  const [showImportModal, setShowImportModal] = useState(false);

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
    
    // Load mock data
    loadMockData();
  }, [certificationId]);

  const loadMockData = () => {
    // Mock questions data
    const mockQuestions: Question[] = [
      {
        id: '1',
        question: 'What is the primary purpose of enterprise architecture?',
        questionAr: 'ما هو الغرض الأساسي من الهندسة المعمارية للمؤسسات؟',
        options: [
          'To design software applications',
          'To align business and IT strategy',
          'To manage databases',
          'To create user interfaces'
        ],
        optionsAr: [
          'لتصميم تطبيقات البرمجيات',
          'لمواءمة استراتيجية الأعمال وتكنولوجيا المعلومات',
          'لإدارة قواعد البيانات',
          'لإنشاء واجهات المستخدم'
        ],
        correctAnswer: 1,
        difficulty: 'medium'
      },
      {
        id: '2',
        question: 'Which phase comes first in TOGAF ADM?',
        questionAr: 'أي مرحلة تأتي أولاً في TOGAF ADM؟',
        options: [
          'Architecture Vision',
          'Business Architecture',
          'Preliminary Phase',
          'Technology Architecture'
        ],
        optionsAr: [
          'رؤية الهندسة المعمارية',
          'هندسة الأعمال',
          'المرحلة التمهيدية',
          'هندسة التكنولوجيا'
        ],
        correctAnswer: 2,
        difficulty: 'easy'
      }
    ];

    // Mock assigned people data
    const mockAssigned: AssignedPerson[] = [
      {
        id: '1',
        name: 'Ahmed Al-Rashid',
        email: '<EMAIL>',
        role: 'Enterprise Architect',
        progress: 75,
        status: 'in-progress',
        assignedDate: '2024-01-15'
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'Solution Architect',
        progress: 100,
        status: 'completed',
        assignedDate: '2024-01-10'
      },
      {
        id: '3',
        name: 'Mohammed Hassan',
        email: '<EMAIL>',
        role: 'Business Analyst',
        progress: 0,
        status: 'not-started',
        assignedDate: '2024-01-20'
      }
    ];

    setQuestions(mockQuestions);
    setAssignedPeople(mockAssigned);
  };

  const certifications: Record<string, Certification> = {
    togaf: {
      id: 'togaf',
      name: 'TOGAF',
      nameAr: 'توجاف',
      description: 'The Open Group Architecture Framework - Enterprise Architecture certification',
      descriptionAr: 'إطار عمل مجموعة المفتوح للهندسة المعمارية - شهادة الهندسة المعمارية للمؤسسات',
      icon: '🏗️',
      color: 'from-blue-500 to-blue-600'
    },
    cdmp: {
      id: 'cdmp',
      name: 'CDMP',
      nameAr: 'سي دي إم بي',
      description: 'Certified Data Management Professional - Data governance and management',
      descriptionAr: 'محترف إدارة البيانات المعتمد - حوكمة وإدارة البيانات',
      icon: '📊',
      color: 'from-green-500 to-green-600'
    },
    cobit: {
      id: 'cobit',
      name: 'COBIT',
      nameAr: 'كوبيت',
      description: 'Control Objectives for Information and Related Technologies',
      descriptionAr: 'أهداف التحكم في المعلومات والتقنيات ذات الصلة',
      icon: '🔒',
      color: 'from-purple-500 to-purple-600'
    },
    cipp: {
      id: 'cipp',
      name: 'CIPP',
      nameAr: 'سي آي بي بي',
      description: 'Certified Information Privacy Professional - Privacy and data protection',
      descriptionAr: 'محترف خصوصية المعلومات المعتمد - الخصوصية وحماية البيانات',
      icon: '🛡️',
      color: 'from-red-500 to-red-600'
    }
  };

  const currentCert = certifications[certificationId];

  const content = {
    en: {
      questionBank: 'Question Bank',
      assignments: 'Assignments',
      importQuestions: 'Import Questions',
      addQuestion: 'Add Question',
      assignPeople: 'Assign People',
      totalQuestions: 'Total Questions',
      assignedPeople: 'Assigned People',
      difficulty: 'Difficulty',
      easy: 'Easy',
      medium: 'Medium',
      hard: 'Hard',
      name: 'Name',
      email: 'Email',
      role: 'Role',
      progress: 'Progress',
      status: 'Status',
      assignedDate: 'Assigned Date',
      notStarted: 'Not Started',
      inProgress: 'In Progress',
      completed: 'Completed',
      importFromExcel: 'Import from Excel',
      selectFile: 'Select Excel File',
      uploadFile: 'Upload File'
    },
    ar: {
      questionBank: 'بنك الأسئلة',
      assignments: 'التكليفات',
      importQuestions: 'استيراد الأسئلة',
      addQuestion: 'إضافة سؤال',
      assignPeople: 'تعيين الأشخاص',
      totalQuestions: 'إجمالي الأسئلة',
      assignedPeople: 'الأشخاص المعينين',
      difficulty: 'الصعوبة',
      easy: 'سهل',
      medium: 'متوسط',
      hard: 'صعب',
      name: 'الاسم',
      email: 'البريد الإلكتروني',
      role: 'الدور',
      progress: 'التقدم',
      status: 'الحالة',
      assignedDate: 'تاريخ التعيين',
      notStarted: 'لم يبدأ',
      inProgress: 'قيد التقدم',
      completed: 'مكتمل',
      importFromExcel: 'استيراد من إكسل',
      selectFile: 'اختر ملف إكسل',
      uploadFile: 'رفع الملف'
    }
  };

  if (!currentCert) {
    return <div>Certification not found</div>;
  }

  const certIcon = (
    <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${currentCert.color} flex items-center justify-center text-white text-lg`}>
      {currentCert.icon}
    </div>
  );

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'not-started': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={language === 'en' ? currentCert.name : currentCert.nameAr}
        subtitle={language === 'en' ? 'Certification Management' : 'إدارة الشهادات'}
        description={language === 'en' ? currentCert.description : currentCert.descriptionAr}
        icon={certIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Training' : 'التدريب', href: '/dashboard/training' },
          { label: language === 'en' ? currentCert.name : currentCert.nameAr }
        ]}
      />

      <div className="bg-gray-50 min-h-screen">
        <div className="px-12 py-8">
          {/* Statistics Cards */}
          <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 mb-8`}>
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {questions.length}
                  </p>
                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].totalQuestions}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {assignedPeople.length}
                  </p>
                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].assignedPeople}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {Math.round(assignedPeople.reduce((acc, person) => acc + person.progress, 0) / assignedPeople.length) || 0}%
                  </p>
                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].progress}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-8">
            <nav className={`flex space-x-8 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {[
                {
                  key: 'questions',
                  label: content[language].questionBank,
                  icon: 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                },
                {
                  key: 'assignments',
                  label: content[language].assignments,
                  icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
                }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-emerald-500 text-emerald-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                >
                  <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tab.icon} />
                  </svg>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="min-h-[600px]">
            {activeTab === 'questions' && (
              <div className="space-y-6">
                {/* Question Bank Header */}
                <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                  <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {content[language].questionBank}
                  </h3>
                  <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <button
                      onClick={() => setShowImportModal(true)}
                      className={`px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                    >
                      <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                      </svg>
                      {content[language].importQuestions}
                    </button>
                    <button
                      className={`px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                    >
                      <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      {content[language].addQuestion}
                    </button>
                  </div>
                </div>

                {/* Questions List */}
                <div className="space-y-4">
                  {questions.map((question, index) => (
                    <div key={question.id} className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                      <div className={`flex items-start justify-between ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                        <div className="flex-1">
                          <div className={`flex items-center mb-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                            <span className={`text-lg font-bold text-gray-400 ${language === 'ar' ? 'ml-4' : 'mr-4'}`}>
                              Q{index + 1}
                            </span>
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty)}`}>
                              {content[language][question.difficulty as keyof typeof content['en']]}
                            </span>
                          </div>
                          <h4 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                            {language === 'en' ? question.question : question.questionAr}
                          </h4>
                          <div className="space-y-2">
                            {(language === 'en' ? question.options : question.optionsAr).map((option, optionIndex) => (
                              <div
                                key={optionIndex}
                                className={`flex items-center p-3 rounded-lg ${
                                  optionIndex === question.correctAnswer
                                    ? 'bg-green-50 border border-green-200'
                                    : 'bg-gray-50 border border-gray-200'
                                } ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}
                              >
                                <span className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${
                                  optionIndex === question.correctAnswer
                                    ? 'bg-green-500 text-white'
                                    : 'bg-gray-300 text-gray-600'
                                } ${language === 'ar' ? 'ml-3' : 'mr-3'}`}>
                                  {String.fromCharCode(65 + optionIndex)}
                                </span>
                                <span className={`${language === 'ar' ? 'font-arabic' : ''}`}>
                                  {option}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                        <div className={`flex space-x-2 ${language === 'ar' ? 'flex-row-reverse space-x-reverse ml-4' : 'mr-4'}`}>
                          <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'assignments' && (
              <div className="space-y-6">
                {/* Assignments Header */}
                <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                  <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {content[language].assignments}
                  </h3>
                  <button
                    className={`px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                  >
                    <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    {content[language].assignPeople}
                  </button>
                </div>

                {/* Assignments Table */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                            {content[language].name}
                          </th>
                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                            {content[language].email}
                          </th>
                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                            {content[language].role}
                          </th>
                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                            {content[language].progress}
                          </th>
                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                            {content[language].status}
                          </th>
                          <th className={`px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                            {content[language].assignedDate}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {assignedPeople.map((person) => (
                          <tr key={person.id} className="hover:bg-gray-50">
                            <td className={`px-6 py-4 whitespace-nowrap ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                              <div className="flex items-center">
                                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center text-white font-medium">
                                  {person.name.charAt(0)}
                                </div>
                                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                                  <div className={`text-sm font-medium text-gray-900 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                    {person.name}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                              {person.email}
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                              {person.role}
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                              <div className="flex items-center">
                                <div className="w-full bg-gray-200 rounded-full h-2 mr-3">
                                  <div
                                    className="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${person.progress}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium text-gray-900">
                                  {person.progress}%
                                </span>
                              </div>
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(person.status)}`}>
                                {content[language][person.status.replace('-', '') as keyof typeof content['en']]}
                              </span>
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                              {new Date(person.assignedDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Import Modal */}
          {showImportModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
                <div className="p-6">
                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <h3 className={`text-xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      {content[language].importFromExcel}
                    </h3>
                    <button
                      onClick={() => setShowImportModal(false)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].selectFile}
                      </label>
                      <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-emerald-400 transition-colors">
                        <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <p className={`text-sm text-gray-600 mb-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
                          {language === 'en' ? 'Drop your Excel file here or click to browse' : 'اسقط ملف الإكسل هنا أو انقر للتصفح'}
                        </p>
                        <input
                          type="file"
                          accept=".xlsx,.xls"
                          className="hidden"
                          id="excel-upload"
                        />
                        <label
                          htmlFor="excel-upload"
                          className="cursor-pointer inline-flex items-center px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors"
                        >
                          {content[language].selectFile}
                        </label>
                      </div>
                    </div>

                    <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <button
                        onClick={() => setShowImportModal(false)}
                        className={`flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors ${language === 'ar' ? 'font-arabic' : ''}`}
                      >
                        {language === 'en' ? 'Cancel' : 'إلغاء'}
                      </button>
                      <button
                        className={`flex-1 px-4 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-200 ${language === 'ar' ? 'font-arabic' : ''}`}
                      >
                        {content[language].uploadFile}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
