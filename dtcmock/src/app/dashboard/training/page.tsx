'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Hero from '../../../components/Hero';

interface Certification {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  icon: string;
  color: string;
  questionsCount: number;
  assignedCount: number;
}

export default function TrainingCourses() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Training Courses',
      subtitle: 'Learning & Development',
      description: 'Manage training programs, educational content, and professional development initiatives for organizational growth.',
      certifications: 'Certifications',
      certificationsDesc: 'Manage certification programs and track progress',
      questions: 'Questions',
      assigned: 'Assigned',
      viewDetails: 'View Details'
    },
    ar: {
      title: 'الدورات التدريبية',
      subtitle: 'التعلم والتطوير',
      description: 'إدارة البرامج التدريبية والمحتوى التعليمي ومبادرات التطوير المهني للنمو التنظيمي.',
      certifications: 'الشهادات',
      certificationsDesc: 'إدارة برامج الشهادات وتتبع التقدم',
      questions: 'الأسئلة',
      assigned: 'المعينين',
      viewDetails: 'عرض التفاصيل'
    }
  };

  const certifications: Certification[] = [
    {
      id: 'togaf',
      name: 'TOGAF',
      nameAr: 'توجاف',
      description: 'The Open Group Architecture Framework - Enterprise Architecture certification',
      descriptionAr: 'إطار عمل مجموعة المفتوح للهندسة المعمارية - شهادة الهندسة المعمارية للمؤسسات',
      icon: '🏗️',
      color: 'from-blue-500 to-blue-600',
      questionsCount: 150,
      assignedCount: 25
    },
    {
      id: 'cdmp',
      name: 'CDMP',
      nameAr: 'سي دي إم بي',
      description: 'Certified Data Management Professional - Data governance and management',
      descriptionAr: 'محترف إدارة البيانات المعتمد - حوكمة وإدارة البيانات',
      icon: '📊',
      color: 'from-green-500 to-green-600',
      questionsCount: 120,
      assignedCount: 18
    },
    {
      id: 'cobit',
      name: 'COBIT',
      nameAr: 'كوبيت',
      description: 'Control Objectives for Information and Related Technologies',
      descriptionAr: 'أهداف التحكم في المعلومات والتقنيات ذات الصلة',
      icon: '🔒',
      color: 'from-purple-500 to-purple-600',
      questionsCount: 100,
      assignedCount: 12
    },
    {
      id: 'cipp',
      name: 'CIPP',
      nameAr: 'سي آي بي بي',
      description: 'Certified Information Privacy Professional - Privacy and data protection',
      descriptionAr: 'محترف خصوصية المعلومات المعتمد - الخصوصية وحماية البيانات',
      icon: '🛡️',
      color: 'from-red-500 to-red-600',
      questionsCount: 80,
      assignedCount: 8
    }
  ];

  const trainingIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={trainingIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-gray-50 min-h-screen">
        <div className="px-12 py-16">
          {/* Certifications Section */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center text-white shadow-lg mr-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                </div>
                <div>
                  <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {content[language].certifications}
                  </h2>
                  <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].certificationsDesc}
                  </p>
                </div>
              </div>
            </div>

            {/* Certifications Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {certifications.map((cert) => (
                <Link
                  key={cert.id}
                  href={`/dashboard/training/${cert.id}`}
                  className="group block"
                >
                  <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    {/* Header with Icon and Gradient */}
                    <div className={`w-full h-32 rounded-xl bg-gradient-to-br ${cert.color} flex items-center justify-center text-4xl mb-6 group-hover:scale-105 transition-transform duration-300`}>
                      {cert.icon}
                    </div>

                    {/* Content */}
                    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <h3 className={`text-xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {language === 'en' ? cert.name : cert.nameAr}
                      </h3>
                      <p className={`text-gray-600 text-sm mb-4 line-clamp-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? cert.description : cert.descriptionAr}
                      </p>

                      {/* Stats */}
                      <div className={`flex justify-between items-center mb-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                        <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                          <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>
                            {cert.questionsCount}
                          </p>
                          <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {content[language].questions}
                          </p>
                        </div>
                        <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                          <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--deep-emerald)' }}>
                            {cert.assignedCount}
                          </p>
                          <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {content[language].assigned}
                          </p>
                        </div>
                      </div>

                      {/* View Details Button */}
                      <div className={`flex items-center justify-center py-2 px-4 rounded-lg bg-gray-50 group-hover:bg-emerald-50 transition-colors ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                        <span className={`text-sm font-medium text-gray-700 group-hover:text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                          {content[language].viewDetails}
                        </span>
                        <svg className={`w-4 h-4 text-gray-400 group-hover:text-emerald-600 transition-colors ${language === 'ar' ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
