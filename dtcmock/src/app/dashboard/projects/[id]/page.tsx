'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Hero from '../../../../components/Hero';

interface Project {
  id: string;
  name: string;
  description: string;
  domain: 'Data' | 'EA';
  country: 'Saudi' | 'Qatar';
  consultant: string;
  projectManager: string;
  status: 'Planning' | 'In Progress' | 'Review' | 'Completed' | 'On Hold';
  startDate: string;
  endDate: string;
  progress: number;
  createdAt: string;
}

export default function ProjectDetail() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [project, setProject] = useState<Project | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'organization' | 'client-context' | 'project-plan'>('overview');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);

    // Mock project data
    const mockProjects: Project[] = [
      {
        id: '1',
        name: 'NDMO Data Governance Implementation',
        description: 'Implementing comprehensive data governance framework based on NDMO standards for government entities.',
        domain: 'Data',
        country: 'Saudi',
        consultant: 'Sarah Johnson',
        projectManager: 'Mohammed Hassan',
        status: 'In Progress',
        startDate: '2024-06-01',
        endDate: '2024-12-31',
        progress: 65,
        createdAt: '2024-05-15'
      },
      {
        id: '2',
        name: 'Qatar GEA Enterprise Architecture',
        description: 'Designing and implementing enterprise architecture framework following GEA guidelines for digital transformation.',
        domain: 'EA',
        country: 'Qatar',
        consultant: 'Omar Abdullah',
        projectManager: 'Fatima Al-Zahra',
        status: 'Planning',
        startDate: '2024-09-01',
        endDate: '2025-03-31',
        progress: 15,
        createdAt: '2024-08-01'
      },
      {
        id: '3',
        name: 'NPC Coding Standards Rollout',
        description: 'Implementing NPC coding standards and best practices across development teams in Qatar.',
        domain: 'Data',
        country: 'Qatar',
        consultant: 'David Wilson',
        projectManager: 'Layla Mansouri',
        status: 'Review',
        startDate: '2024-04-01',
        endDate: '2024-10-31',
        progress: 85,
        createdAt: '2024-03-15'
      },
      {
        id: '4',
        name: 'NORA Infrastructure Modernization',
        description: 'Modernizing national digital infrastructure following NORA framework specifications.',
        domain: 'EA',
        country: 'Saudi',
        consultant: 'Sarah Johnson',
        projectManager: 'Mohammed Hassan',
        status: 'Completed',
        startDate: '2024-01-01',
        endDate: '2024-07-31',
        progress: 100,
        createdAt: '2023-12-01'
      },
      {
        id: '5',
        name: 'Cross-Border Data Integration',
        description: 'Establishing secure data integration protocols between Saudi and Qatar government systems.',
        domain: 'Data',
        country: 'Saudi',
        consultant: 'Omar Abdullah',
        projectManager: 'Fatima Al-Zahra',
        status: 'On Hold',
        startDate: '2024-08-01',
        endDate: '2025-02-28',
        progress: 25,
        createdAt: '2024-07-10'
      }
    ];

    const foundProject = mockProjects.find(p => p.id === projectId);
    setProject(foundProject || null);
  }, [projectId]);

  const content = {
    en: {
      projectDetails: 'Project Details',
      backToProjects: 'Back to Projects',
      overview: 'Overview',
      organization: 'Organization Structure',
      clientContext: 'Client Context',
      projectPlan: 'Project Plan',
      projectNotFound: 'Project Not Found',
      projectNotFoundDesc: 'The requested project could not be found.',
      domain: 'Domain',
      country: 'Country',
      consultant: 'Consultant',
      projectManager: 'Project Manager',
      status: 'Status',
      progress: 'Progress',
      startDate: 'Start Date',
      endDate: 'End Date',
      dataManagement: 'Data Management',
      enterpriseArchitecture: 'Enterprise Architecture',
      saudiArabia: 'Saudi Arabia',
      qatar: 'Qatar'
    },
    ar: {
      projectDetails: 'تفاصيل المشروع',
      backToProjects: 'العودة للمشاريع',
      overview: 'نظرة عامة',
      organization: 'الهيكل التنظيمي',
      clientContext: 'سياق العميل',
      projectPlan: 'خطة المشروع',
      projectNotFound: 'المشروع غير موجود',
      projectNotFoundDesc: 'لم يتم العثور على المشروع المطلوب.',
      domain: 'المجال',
      country: 'الدولة',
      consultant: 'المستشار',
      projectManager: 'مدير المشروع',
      status: 'الحالة',
      progress: 'التقدم',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      dataManagement: 'إدارة البيانات',
      enterpriseArchitecture: 'هندسة المؤسسة',
      saudiArabia: 'المملكة العربية السعودية',
      qatar: 'قطر'
    }
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'Planning': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Review': return 'bg-purple-100 text-purple-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'On Hold': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {
    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';
  };

  const projectIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  if (!project) {
    return (
      <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
        <div className="bg-white min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div
              className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
              style={{ backgroundColor: 'var(--charcoal-grey)' }}
            >
              <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].projectNotFound}
            </h2>
            <p className={`text-xl text-gray-600 max-w-2xl mx-auto mb-8 ${language === 'ar' ? 'font-arabic' : ''}`}>
              {content[language].projectNotFoundDesc}
            </p>
            <Link
              href="/dashboard/projects"
              className={`inline-flex items-center px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
              style={{ backgroundColor: 'var(--emerald-green)' }}
            >
              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {content[language].backToProjects}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={project.name}
        subtitle={content[language].projectDetails}
        description={project.description}
        icon={projectIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Projects' : 'المشاريع', href: '/dashboard/projects' },
          { label: project.name }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-8">
          {/* Back Button */}
          <div className="mb-8">
            <Link
              href="/dashboard/projects"
              className={`inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
            >
              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {content[language].backToProjects}
            </Link>
          </div>

          {/* Project Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Domain Card */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].domain}</p>
                  <p className={`text-lg font-bold text-blue-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {project.domain === 'Data' ? content[language].dataManagement : content[language].enterpriseArchitecture}
                  </p>
                </div>
              </div>
            </div>

            {/* Country Card */}
            <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <span className="text-lg">{getCountryFlag(project.country)}</span>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].country}</p>
                  <p className={`text-lg font-bold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {project.country === 'Saudi' ? content[language].saudiArabia : content[language].qatar}
                  </p>
                </div>
              </div>
            </div>

            {/* Status Card */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].status}</p>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)} ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {project.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Progress Card */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-orange-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].progress}</p>
                  <p className={`text-lg font-bold text-orange-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{project.progress}%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-8">
            <nav className={`flex space-x-8 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {[
                { key: 'overview', label: content[language].overview, icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
                { key: 'organization', label: content[language].organization, icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
                { key: 'client-context', label: content[language].clientContext, icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2' },
                { key: 'project-plan', label: content[language].projectPlan, icon: 'M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-emerald-500 text-emerald-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                >
                  <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tab.icon} />
                  </svg>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="min-h-[600px]">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Project Information */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    Project Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].consultant}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.consultant}</p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].projectManager}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.projectManager}</p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].startDate}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.startDate}</p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].endDate}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.endDate}</p>
                    </div>
                  </div>
                </div>

                {/* Progress Visualization */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    Progress Overview
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className={`text-lg font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        Overall Progress
                      </span>
                      <span className={`text-lg font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>
                        {project.progress}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div
                        className="h-4 rounded-full transition-all duration-300"
                        style={{
                          width: `${project.progress}%`,
                          background: 'linear-gradient(90deg, var(--emerald-green), var(--deep-emerald))'
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'organization' && (
              <div className="text-center py-16">
                <div
                  className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
                  style={{ backgroundColor: 'var(--emerald-green)' }}
                >
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {language === 'en' ? 'Organization Structure' : 'الهيكل التنظيمي'}
                </h3>
                <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {language === 'en'
                    ? 'Organization structure visualization with import from Excel capability coming soon.'
                    : 'تصور الهيكل التنظيمي مع إمكانية الاستيراد من Excel قريباً.'
                  }
                </p>
              </div>
            )}

            {activeTab === 'client-context' && (
              <div className="text-center py-16">
                <div
                  className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
                  style={{ backgroundColor: 'var(--emerald-green)' }}
                >
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2" />
                  </svg>
                </div>
                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {language === 'en' ? 'Client Context' : 'سياق العميل'}
                </h3>
                <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {language === 'en'
                    ? 'Client context management with culture, politics, and stakeholder information coming soon.'
                    : 'إدارة سياق العميل مع معلومات الثقافة والسياسة وأصحاب المصلحة قريباً.'
                  }
                </p>
              </div>
            )}

            {activeTab === 'project-plan' && (
              <div className="text-center py-16">
                <div
                  className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
                  style={{ backgroundColor: 'var(--emerald-green)' }}
                >
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {language === 'en' ? 'Project Plan' : 'خطة المشروع'}
                </h3>
                <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {language === 'en'
                    ? 'Advanced project planning with Gantt charts and dependency management coming soon.'
                    : 'تخطيط المشاريع المتقدم مع مخططات جانت وإدارة التبعيات قريباً.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
